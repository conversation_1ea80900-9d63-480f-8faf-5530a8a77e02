<Window x:Class="ProjectManagementApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="نظام إدارة المشاريع" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#f5f5f5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2E7D32" CornerRadius="0,0,10,10">
            <Grid>
                <TextBlock Text="مؤسسة تمديد شباب" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          HorizontalAlignment="Center" 
                          VerticalAlignment="Center"/>
                
                <!-- Excel-like ribbon style -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Left" 
                           VerticalAlignment="Center" 
                           Margin="20,0">
                    <materialDesign:PackIcon Kind="Microsoft" 
                                           Width="32" 
                                           Height="32" 
                                           Foreground="White"/>
                    <TextBlock Text="Project Management System" 
                              FontSize="12" 
                              Foreground="White" 
                              VerticalAlignment="Center" 
                              Margin="10,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content Area -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Left Menu Panel -->
            <Border Grid.Column="0"
                    Background="White"
                    CornerRadius="10"
                    Margin="0,0,10,0">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="5" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel Margin="20">
                    <TextBlock Text="القائمة الرئيسية" 
                              FontSize="18" 
                              FontWeight="Bold" 
                              Margin="0,0,0,20" 
                              HorizontalAlignment="Center"/>
                    
                    <!-- Project Management Button -->
                    <Button Name="ProjectManagementBtn" 
                            Style="{StaticResource MenuButtonStyle}"
                            Click="ProjectManagementBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FolderMultiple" 
                                                   Width="24" 
                                                   Height="24" 
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="إدارة المشاريع" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <!-- Data Management Button -->
                    <Button Name="DataManagementBtn" 
                            Style="{StaticResource MenuButtonStyle}"
                            Click="DataManagementBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Database" 
                                                   Width="24" 
                                                   Height="24" 
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="إدارة البيانات والمعلومات" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <!-- Reports Button -->
                    <Button Name="ReportsBtn" 
                            Style="{StaticResource MenuButtonStyle}"
                            Click="ReportsBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileChart" 
                                                   Width="24" 
                                                   Height="24" 
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="التقارير" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <!-- Users Management Button -->
                    <Button Name="UsersBtn" 
                            Style="{StaticResource MenuButtonStyle}"
                            Click="UsersBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountMultiple" 
                                                   Width="24" 
                                                   Height="24" 
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="المستخدمين" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Border>
            
            <!-- Right Content Area -->
            <Border Grid.Column="1"
                    Background="White"
                    CornerRadius="10">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="5" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Content Header -->
                    <Border Grid.Row="0" 
                            Background="#E8F5E8" 
                            CornerRadius="10,10,0,0">
                        <TextBlock Name="ContentTitle" 
                                  Text="مرحباً بك في نظام إدارة المشاريع" 
                                  FontSize="20" 
                                  FontWeight="Bold" 
                                  HorizontalAlignment="Center" 
                                  VerticalAlignment="Center"/>
                    </Border>
                    
                    <!-- Content Area -->
                    <ScrollViewer Grid.Row="1" 
                                 VerticalScrollBarVisibility="Auto" 
                                 Margin="20">
                        <ContentControl Name="MainContent">
                            <!-- Welcome Content -->
                            <StackPanel>
                                <TextBlock Text="أهلاً وسهلاً" 
                                          FontSize="24" 
                                          FontWeight="Bold" 
                                          HorizontalAlignment="Center" 
                                          Margin="0,50,0,20"/>
                                
                                <TextBlock Text="اختر من القائمة الجانبية للبدء:" 
                                          FontSize="16" 
                                          HorizontalAlignment="Center" 
                                          Margin="0,0,0,30"/>
                                
                                <!-- Quick Stats Cards -->
                                <UniformGrid Columns="2" Margin="0,20">
                                    <Border Background="#4CAF50" 
                                           CornerRadius="8" 
                                           Margin="10" 
                                           Padding="20">
                                        <StackPanel HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="FolderMultiple" 
                                                                   Width="48" 
                                                                   Height="48" 
                                                                   Foreground="White" 
                                                                   HorizontalAlignment="Center"/>
                                            <TextBlock Text="المشاريع النشطة" 
                                                      FontSize="16" 
                                                      Foreground="White" 
                                                      HorizontalAlignment="Center" 
                                                      Margin="0,10,0,5"/>
                                            <TextBlock Text="12" 
                                                      FontSize="24" 
                                                      FontWeight="Bold" 
                                                      Foreground="White" 
                                                      HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Background="#2196F3" 
                                           CornerRadius="8" 
                                           Margin="10" 
                                           Padding="20">
                                        <StackPanel HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountMultiple" 
                                                                   Width="48" 
                                                                   Height="48" 
                                                                   Foreground="White" 
                                                                   HorizontalAlignment="Center"/>
                                            <TextBlock Text="المستخدمين" 
                                                      FontSize="16" 
                                                      Foreground="White" 
                                                      HorizontalAlignment="Center" 
                                                      Margin="0,10,0,5"/>
                                            <TextBlock Text="25" 
                                                      FontSize="24" 
                                                      FontWeight="Bold" 
                                                      Foreground="White" 
                                                      HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </UniformGrid>
                            </StackPanel>
                        </ContentControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#2E7D32">
            <TextBlock Text="© 2025 - احمد العمدي  نظام إدارة المشاريع - جميع الحقوق محفوظة" 
                      FontSize="12" 
                      Foreground="White" 
                      HorizontalAlignment="Center" 
                      VerticalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
