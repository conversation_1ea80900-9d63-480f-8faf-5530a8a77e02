using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace ProjectManagementApp
{
    /// <summary>
    /// Interaction logic for AddProjectWindow.xaml
    /// </summary>
    public partial class AddProjectWindow : Window
    {
        public Project NewProject { get; private set; }
        public bool IsProjectSaved { get; private set; } = false;

        public AddProjectWindow()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Set default values
            ProjectStatusComboBox.SelectedIndex = 0; // Default to "نشط"
            StartDatePicker.SelectedDate = DateTime.Today;
            EndDatePicker.SelectedDate = DateTime.Today.AddMonths(6); // Default 6 months project
            
            // Focus on first field
            ProjectIdTextBox.Focus();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                try
                {
                    // Create new project object
                    NewProject = new Project
                    {
                        ProjectId = ProjectIdTextBox.Text.Trim(),
                        ProjectName = ProjectNameTextBox.Text.Trim(),
                        ProjectManager = ProjectManagerTextBox.Text.Trim(),
                        Status = ((ComboBoxItem)ProjectStatusComboBox.SelectedItem).Content.ToString(),
                        StartDate = StartDatePicker.SelectedDate.Value,
                        EndDate = EndDatePicker.SelectedDate.Value,
                        Budget = decimal.TryParse(ProjectBudgetTextBox.Text, out decimal budget) ? budget : 0,
                        Description = ProjectDescriptionTextBox.Text.Trim()
                    };

                    IsProjectSaved = true;

                    // Show success message
                    MessageBox.Show("تم حفظ المشروع بنجاح!", "نجح الحفظ", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);

                    this.DialogResult = true;
                    this.Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء حفظ المشروع:\n{ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إلغاء إضافة المشروع؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                       "تأكيد الإلغاء", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateForm()
        {
            var errors = new List<string>();

            // Validate required fields
            if (string.IsNullOrWhiteSpace(ProjectIdTextBox.Text))
                errors.Add("• رقم المشروع مطلوب");

            if (string.IsNullOrWhiteSpace(ProjectNameTextBox.Text))
                errors.Add("• اسم المشروع مطلوب");

            if (string.IsNullOrWhiteSpace(ProjectManagerTextBox.Text))
                errors.Add("• مدير المشروع مطلوب");

            if (ProjectStatusComboBox.SelectedItem == null)
                errors.Add("• حالة المشروع مطلوبة");

            if (!StartDatePicker.SelectedDate.HasValue)
                errors.Add("• تاريخ البداية مطلوب");

            if (!EndDatePicker.SelectedDate.HasValue)
                errors.Add("• تاريخ النهاية مطلوب");

            // Validate date logic
            if (StartDatePicker.SelectedDate.HasValue && EndDatePicker.SelectedDate.HasValue)
            {
                if (EndDatePicker.SelectedDate.Value <= StartDatePicker.SelectedDate.Value)
                    errors.Add("• تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
            }

            // Validate budget if entered
            if (!string.IsNullOrWhiteSpace(ProjectBudgetTextBox.Text))
            {
                if (!decimal.TryParse(ProjectBudgetTextBox.Text, out decimal budget) || budget < 0)
                    errors.Add("• ميزانية المشروع يجب أن تكون رقماً صحيحاً وأكبر من أو يساوي صفر");
            }

            // Show validation errors
            if (errors.Any())
            {
                var errorMessage = "يرجى تصحيح الأخطاء التالية:\n\n" + string.Join("\n", errors);
                MessageBox.Show(errorMessage, "أخطاء في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        // Handle Enter key to move to next field
        private void ProjectIdTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
                ProjectNameTextBox.Focus();
        }

        private void ProjectNameTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
                ProjectManagerTextBox.Focus();
        }

        private void ProjectManagerTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
                ProjectBudgetTextBox.Focus();
        }

        private void ProjectBudgetTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
                StartDatePicker.Focus();
        }
    }

    // Project data model
    public class Project
    {
        public string ProjectId { get; set; }
        public string ProjectName { get; set; }
        public string ProjectManager { get; set; }
        public string Status { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Budget { get; set; }
        public string Description { get; set; }

        public string StartDateFormatted => StartDate.ToString("yyyy/MM/dd");
        public string EndDateFormatted => EndDate.ToString("yyyy/MM/dd");
        public string BudgetFormatted => Budget.ToString("N0") + " ريال";
    }
}
