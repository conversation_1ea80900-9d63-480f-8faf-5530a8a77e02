using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace ProjectManagementApp
{
    /// <summary>
    /// Interaction logic for DataTypesEditorWindow.xaml
    /// </summary>
    public partial class DataTypesEditorWindow : Window
    {
        private IpttIndicator currentIndicator;
        private List<string> monthColumns;
        private ObservableCollection<DataTypeItem> workingDataTypes;

        public DataTypesEditorWindow(IpttIndicator indicator, List<string> months)
        {
            InitializeComponent();
            currentIndicator = indicator;
            monthColumns = months;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Set indicator name in header
            IndicatorNameText.Text = $"المؤشر: {currentIndicator.Indicator}";
            
            // Create working copy of data types
            workingDataTypes = new ObservableCollection<DataTypeItem>();
            foreach (var dataType in currentIndicator.DataTypes)
            {
                var workingDataType = new DataTypeItem
                {
                    Name = dataType.Name,
                    Target = dataType.Target,
                    MonthlyValues = new Dictionary<string, string>(dataType.MonthlyValues)
                };
                workingDataTypes.Add(workingDataType);
            }
            
            // Bind to ItemsControl
            DataTypesItemsControl.ItemsSource = workingDataTypes;
            
            // Add default data types if none exist
            if (workingDataTypes.Count == 0)
            {
                AddDefaultDataTypes();
            }
        }

        private void AddDefaultDataTypes()
        {
            var defaultTypes = new List<string>
            {
                "إجمالي",
                "# من الرجال",
                "# من النساء",
                "# من الأولاد",
                "# من البنات"
            };

            foreach (var typeName in defaultTypes)
            {
                var dataType = new DataTypeItem
                {
                    Name = typeName,
                    Target = "0",
                    MonthlyValues = new Dictionary<string, string>()
                };

                // Initialize monthly values
                foreach (var month in monthColumns)
                {
                    dataType.MonthlyValues[month] = "0";
                }

                workingDataTypes.Add(dataType);
            }
        }

        private void AddDataTypeButton_Click(object sender, RoutedEventArgs e)
        {
            var name = NewDataTypeTextBox.Text.Trim();
            var target = NewDataTypeTargetTextBox.Text.Trim();

            if (string.IsNullOrEmpty(name))
            {
                MessageBox.Show("يرجى إدخال اسم نوع البيانات", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Check if data type already exists
            if (workingDataTypes.Any(dt => dt.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("نوع البيانات موجود بالفعل", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var newDataType = new DataTypeItem
            {
                Name = name,
                Target = string.IsNullOrEmpty(target) ? "0" : target,
                MonthlyValues = new Dictionary<string, string>()
            };

            // Initialize monthly values
            foreach (var month in monthColumns)
            {
                newDataType.MonthlyValues[month] = "0";
            }

            workingDataTypes.Add(newDataType);

            // Clear input fields
            NewDataTypeTextBox.Text = "";
            NewDataTypeTargetTextBox.Text = "";
        }

        private void DeleteDataTypeButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var dataType = button?.Tag as DataTypeItem;

            if (dataType != null)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف نوع البيانات '{dataType.Name}'؟", 
                                           "تأكيد الحذف", 
                                           MessageBoxButton.YesNo, 
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    workingDataTypes.Remove(dataType);
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate that at least one data type exists
                if (workingDataTypes.Count == 0)
                {
                    MessageBox.Show("يجب إضافة نوع بيانات واحد على الأقل", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Validate that all data types have names
                var emptyNames = workingDataTypes.Where(dt => string.IsNullOrWhiteSpace(dt.Name)).ToList();
                if (emptyNames.Any())
                {
                    MessageBox.Show("يرجى إدخال أسماء لجميع أنواع البيانات", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Update the original indicator
                currentIndicator.DataTypes.Clear();
                foreach (var workingDataType in workingDataTypes)
                {
                    var originalDataType = new DataTypeItem
                    {
                        Name = workingDataType.Name.Trim(),
                        Target = workingDataType.Target?.Trim() ?? "0",
                        MonthlyValues = new Dictionary<string, string>(workingDataType.MonthlyValues)
                    };
                    currentIndicator.DataTypes.Add(originalDataType);
                }

                // Update total target (sum of all targets if numeric)
                UpdateTotalTarget();

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTotalTarget()
        {
            try
            {
                decimal totalTarget = 0;
                bool hasNumericTargets = false;

                foreach (var dataType in currentIndicator.DataTypes)
                {
                    if (decimal.TryParse(dataType.Target?.Replace(",", ""), out decimal target))
                    {
                        totalTarget += target;
                        hasNumericTargets = true;
                    }
                }

                if (hasNumericTargets)
                {
                    currentIndicator.TotalTarget = totalTarget.ToString("N0");
                }
            }
            catch
            {
                // If calculation fails, keep original target
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                CancelButton_Click(sender, e);
            }
            else if (e.Key == Key.Enter && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
            {
                SaveButton_Click(sender, e);
            }
        }
    }
}
