<Window x:Class="ProjectManagementApp.IpttDesignWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="تصميم IPTT - جدول المؤشرات والأهداف" 
        Height="900" 
        Width="1400"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowState="Maximized">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="100"/>
            <RowDefinition Height="120"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="70"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#FF9800" CornerRadius="0,0,15,15">
            <Grid>
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="TableLarge" 
                                           Width="40" 
                                           Height="40" 
                                           Foreground="White"
                                           Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Name="ProjectTitleText"
                                  Text="تصميم IPTT - جدول المؤشرات والأهداف" 
                                  FontSize="24" 
                                  FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Name="ProjectNameText"
                                  FontSize="16" 
                                  Foreground="White" 
                                  Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Project Info Card -->
        <Border Grid.Row="1" 
                Background="White" 
                Margin="20,10,20,10"
                CornerRadius="10"
                Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="عنوان المشروع:" FontWeight="Bold" FontSize="14" Foreground="#333"/>
                    <TextBlock Name="ProjectTitle" FontSize="12" Margin="0,2,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="مدير المشروع:" FontWeight="Bold" FontSize="14" Foreground="#333"/>
                    <TextBlock Name="ProjectManager" FontSize="12" Margin="0,2,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="مدة المشروع:" FontWeight="Bold" FontSize="14" Foreground="#333"/>
                    <TextBlock Name="ProjectDuration" FontSize="12" Margin="0,2,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3">
                    <TextBlock Text="مواقع المشروع:" FontWeight="Bold" FontSize="14" Foreground="#333"/>
                    <TextBlock Text="محافظة إب (مديرية السياني)" FontSize="12" Margin="0,2,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- IPTT Table -->
        <Border Grid.Row="2" 
                Background="White" 
                Margin="20,0,20,10"
                CornerRadius="10"
                Padding="15">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.2"/>
            </Border.Effect>
            
            <ScrollViewer HorizontalScrollBarVisibility="Auto" 
                         VerticalScrollBarVisibility="Auto">
                <DataGrid Name="IpttDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="True"
                         CanUserDeleteRows="True"
                         CanUserReorderColumns="False"
                         CanUserResizeColumns="True"
                         CanUserResizeRows="False"
                         CanUserSortColumns="False"
                         GridLinesVisibility="All"
                         HeadersVisibility="All"
                         SelectionMode="Single"
                         SelectionUnit="Cell"
                         Background="White"
                         RowBackground="White"
                         AlternatingRowBackground="#f9f9f9"
                         BorderBrush="#ddd"
                         BorderThickness="1"
                         FontSize="11"
                         MinRowHeight="25">
                    
                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#FF9800"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="10"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="5,8"/>
                            <Setter Property="BorderBrush" Value="White"/>
                            <Setter Property="BorderThickness" Value="1"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>
                    
                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderBrush" Value="#ddd"/>
                            <Setter Property="BorderThickness" Value="0.5"/>
                            <Setter Property="Padding" Value="3"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                    <Setter Property="Foreground" Value="Black"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.CellStyle>
                    
                    <!-- Columns will be generated dynamically in code-behind -->
                </DataGrid>
            </ScrollViewer>
        </Border>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="3" 
                Background="White" 
                BorderBrush="#e0e0e0" 
                BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                
                <Button Name="SaveIpttButton" 
                        Content="حفظ IPTT" 
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="SaveIpttButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="ContentSave" 
                                                           Width="20" 
                                                           Height="20" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#45a049"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#3d8b40"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
                
                <Button Name="ExportExcelButton" 
                        Content="تصدير إلى Excel" 
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#2196F3"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="ExportExcelButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="MicrosoftExcel" 
                                                           Width="20" 
                                                           Height="20" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1976D2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#1565C0"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
                
                <Button Name="AddIndicatorButton" 
                        Content="إضافة مؤشر" 
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#9C27B0"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="AddIndicatorButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Plus" 
                                                           Width="20" 
                                                           Height="20" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#7B1FA2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#6A1B9A"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
                
                <Button Name="CloseButton" 
                        Content="إغلاق" 
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#f44336"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="CloseButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Close" 
                                                           Width="20" 
                                                           Height="20" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#da190b"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#c62828"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
