<Window x:Class="ProjectManagementApp.DataTypesEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="تحرير أنواع البيانات" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        ResizeMode="CanResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="70"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#9C27B0" CornerRadius="0,0,15,15">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="FormatListBulleted" 
                                       Width="30" 
                                       Height="30" 
                                       Foreground="White"
                                       Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="تحرير أنواع البيانات" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White"/>
                    <TextBlock Name="IndicatorNameText"
                              FontSize="12" 
                              Foreground="White" 
                              Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- Content -->
        <Border Grid.Row="1" 
                Background="White" 
                Margin="20,10,20,10"
                CornerRadius="10"
                Padding="15">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Add New Data Type Section -->
                <Border Grid.Row="0" 
                        Background="#f5f5f5" 
                        CornerRadius="8"
                        Padding="10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="100"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Name="NewDataTypeTextBox"
                                Grid.Column="0"
                                materialDesign:HintAssist.Hint="اسم نوع البيانات الجديد"
                                FontSize="14"
                                Margin="0,0,10,0"
                                VerticalAlignment="Center"/>
                        
                        <TextBox Name="NewDataTypeTargetTextBox"
                                Grid.Column="1"
                                materialDesign:HintAssist.Hint="الهدف"
                                FontSize="14"
                                Margin="0,0,10,0"
                                VerticalAlignment="Center"/>
                        
                        <Button Name="AddDataTypeButton"
                                Grid.Column="2"
                                Content="إضافة"
                                Background="#4CAF50"
                                Foreground="White"
                                FontSize="14"
                                FontWeight="Bold"
                                BorderThickness="0"
                                Height="35"
                                Cursor="Hand"
                                Click="AddDataTypeButton_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                            CornerRadius="6"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            BorderBrush="{TemplateBinding BorderBrush}">
                                        <StackPanel Orientation="Horizontal" 
                                                   HorizontalAlignment="Center" 
                                                   VerticalAlignment="Center">
                                            <materialDesign:PackIcon Kind="Plus" 
                                                                   Width="16" 
                                                                   Height="16" 
                                                                   Margin="0,0,5,0"/>
                                            <ContentPresenter/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#45a049"/>
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter Property="Background" Value="#3d8b40"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </Grid>
                </Border>
                
                <!-- Data Types List -->
                <ScrollViewer Grid.Row="1" 
                             VerticalScrollBarVisibility="Auto"
                             Margin="0,10,0,0">
                    <ItemsControl Name="DataTypesItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="White" 
                                        BorderBrush="#e0e0e0" 
                                        BorderThickness="1"
                                        CornerRadius="8"
                                        Margin="0,0,0,10"
                                        Padding="15">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="10"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <!-- Data Type Header -->
                                        <Grid Grid.Row="0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="80"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBox Grid.Column="0"
                                                    Text="{Binding Name, Mode=TwoWay}"
                                                    FontSize="14"
                                                    FontWeight="Bold"
                                                    Margin="0,0,10,0"
                                                    materialDesign:HintAssist.Hint="اسم نوع البيانات"/>
                                            
                                            <TextBox Grid.Column="1"
                                                    Text="{Binding Target, Mode=TwoWay}"
                                                    FontSize="14"
                                                    Margin="0,0,10,0"
                                                    materialDesign:HintAssist.Hint="الهدف"/>
                                            
                                            <Button Grid.Column="2"
                                                   Content="حذف"
                                                   Background="#f44336"
                                                   Foreground="White"
                                                   FontSize="12"
                                                   BorderThickness="0"
                                                   Height="30"
                                                   Cursor="Hand"
                                                   Click="DeleteDataTypeButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Template>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}" 
                                                                CornerRadius="6"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                BorderBrush="{TemplateBinding BorderBrush}">
                                                            <StackPanel Orientation="Horizontal" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center">
                                                                <materialDesign:PackIcon Kind="Delete" 
                                                                                       Width="14" 
                                                                                       Height="14" 
                                                                                       Margin="0,0,3,0"/>
                                                                <ContentPresenter/>
                                                            </StackPanel>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#da190b"/>
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter Property="Background" Value="#c62828"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Button.Template>
                                            </Button>
                                        </Grid>
                                        
                                        <!-- Monthly Values (if needed in future) -->
                                        <TextBlock Grid.Row="2"
                                                  Text="يمكن إضافة القيم الشهرية لاحقاً في الجدول الرئيسي"
                                                  FontSize="10"
                                                  Foreground="Gray"
                                                  FontStyle="Italic"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" 
                Background="White" 
                BorderBrush="#e0e0e0" 
                BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                
                <Button Name="SaveButton" 
                        Content="حفظ التغييرات" 
                        Width="140"
                        Height="40"
                        Margin="10"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="14"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="SaveButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="ContentSave" 
                                                           Width="18" 
                                                           Height="18" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#45a049"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#3d8b40"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
                
                <Button Name="CancelButton" 
                        Content="إلغاء" 
                        Width="140"
                        Height="40"
                        Margin="10"
                        Background="#f44336"
                        Foreground="White"
                        FontSize="14"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="CancelButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Close" 
                                                           Width="18" 
                                                           Height="18" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#da190b"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#c62828"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
