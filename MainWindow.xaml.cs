using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace ProjectManagementApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private List<Project> projects;

        public MainWindow()
        {
            InitializeComponent();
            InitializeProjects();
        }

        private void InitializeProjects()
        {
            // Initialize with sample data
            projects = new List<Project>
            {
                new Project
                {
                    ProjectId = "CBPF-YEM-23-R",
                    ProjectName = "مشروع التغذية والصحة",
                    ProjectManager = "د. جمال سيف",
                    StartDate = new DateTime(2024, 1, 1),
                    EndDate = new DateTime(2024, 12, 31),
                    Status = "نشط",
                    Budget = 500000,
                    Description = "مشروع شامل للتغذية والصحة في المناطق المحتاجة"
                },
                new Project
                {
                    ProjectId = "WASH-001",
                    ProjectName = "مشروع المياه والصرف الصحي",
                    ProjectManager = "أحمد محمد",
                    StartDate = new DateTime(2024, 2, 1),
                    EndDate = new DateTime(2024, 11, 30),
                    Status = "نشط",
                    Budget = 750000,
                    Description = "توفير المياه النظيفة وأنظمة الصرف الصحي"
                },
                new Project
                {
                    ProjectId = "EDU-002",
                    ProjectName = "مشروع التعليم",
                    ProjectManager = "فاطمة علي",
                    StartDate = new DateTime(2024, 3, 1),
                    EndDate = new DateTime(2024, 10, 31),
                    Status = "قيد التنفيذ",
                    Budget = 300000,
                    Description = "تحسين جودة التعليم في المدارس الريفية"
                }
            };
        }

        private void ProjectManagementBtn_Click(object sender, RoutedEventArgs e)
        {
            ContentTitle.Text = "إدارة المشاريع";
            
            // Create project management content
            var projectContent = new StackPanel();
            
            // Header
            var header = new TextBlock
            {
                Text = "إدارة المشاريع",
                FontSize = 22,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            projectContent.Children.Add(header);
            
            // Project list simulation (like Excel data)
            var dataGrid = new DataGrid
            {
                Height = 400,
                Margin = new Thickness(0, 20, 0, 0),
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                GridLinesVisibility = DataGridGridLinesVisibility.All
            };
            
            // Add columns
            dataGrid.Columns.Add(new DataGridTextColumn 
            { 
                Header = "رقم المشروع", 
                Binding = new Binding("ProjectId"),
                Width = 100
            });
            dataGrid.Columns.Add(new DataGridTextColumn 
            { 
                Header = "اسم المشروع", 
                Binding = new Binding("ProjectName"),
                Width = 200
            });
            dataGrid.Columns.Add(new DataGridTextColumn 
            { 
                Header = "مدير المشروع", 
                Binding = new Binding("ProjectManager"),
                Width = 150
            });
            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "تاريخ البداية",
                Binding = new Binding("StartDateFormatted"),
                Width = 120
            });
            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "تاريخ النهاية",
                Binding = new Binding("EndDateFormatted"),
                Width = 120
            });
            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الميزانية",
                Binding = new Binding("BudgetFormatted"),
                Width = 120
            });
            dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الحالة",
                Binding = new Binding("Status"),
                Width = 100
            });

            // Add IPTT Design button column
            var ipttColumn = new DataGridTemplateColumn
            {
                Header = "تصميم IPTT",
                Width = 120
            };

            var buttonTemplate = new DataTemplate();
            var buttonFactory = new FrameworkElementFactory(typeof(Button));
            buttonFactory.SetValue(Button.ContentProperty, "تصميم IPTT");
            buttonFactory.SetValue(Button.BackgroundProperty, new SolidColorBrush(Color.FromRgb(255, 152, 0)));
            buttonFactory.SetValue(Button.ForegroundProperty, Brushes.White);
            buttonFactory.SetValue(Button.BorderThicknessProperty, new Thickness(0, 0, 0, 0));
            buttonFactory.SetValue(Button.PaddingProperty, new Thickness(8, 4, 8, 4));
            buttonFactory.SetValue(Button.FontSizeProperty, 12.0);
            buttonFactory.SetValue(Button.FontWeightProperty, FontWeights.Bold);
            buttonFactory.SetValue(Button.CursorProperty, Cursors.Hand);
            buttonFactory.AddHandler(Button.ClickEvent, new RoutedEventHandler(IpttDesignButton_Click));

            buttonTemplate.VisualTree = buttonFactory;
            ipttColumn.CellTemplate = buttonTemplate;
            dataGrid.Columns.Add(ipttColumn);
            
            // Bind to projects list
            dataGrid.ItemsSource = projects;
            projectContent.Children.Add(dataGrid);
            
            // Action buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 20, 0, 0)
            };
            
            var addBtn = new Button
            {
                Content = "إضافة مشروع جديد",
                Margin = new Thickness(10, 10, 10, 10),
                Padding = new Thickness(15, 8, 15, 8),
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0, 0, 0, 0)
            };
            addBtn.Click += AddProjectBtn_Click;

            var editBtn = new Button
            {
                Content = "تعديل المشروع",
                Margin = new Thickness(10, 10, 10, 10),
                Padding = new Thickness(15, 8, 15, 8),
                Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0, 0, 0, 0)
            };
            
            buttonPanel.Children.Add(addBtn);
            buttonPanel.Children.Add(editBtn);
            projectContent.Children.Add(buttonPanel);
            
            MainContent.Content = projectContent;
        }

        private void DataManagementBtn_Click(object sender, RoutedEventArgs e)
        {
            ContentTitle.Text = "إدارة البيانات والمعلومات";
            
            var dataContent = new StackPanel();
            
            var header = new TextBlock
            {
                Text = "إدارة البيانات والمعلومات",
                FontSize = 22,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            dataContent.Children.Add(header);
            
            // Data management options
            var optionsGrid = new Grid();
            optionsGrid.ColumnDefinitions.Add(new ColumnDefinition());
            optionsGrid.ColumnDefinitions.Add(new ColumnDefinition());
            optionsGrid.Margin = new Thickness(0, 20, 0, 0);
            
            // Import Data Card
            var importCard = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(10, 10, 10, 10),
                Padding = new Thickness(20, 20, 20, 20),
                Cursor = Cursors.Hand
            };
            Grid.SetColumn(importCard, 0);
            
            var importStack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            importStack.Children.Add(new TextBlock 
            { 
                Text = "📊", 
                FontSize = 48, 
                HorizontalAlignment = HorizontalAlignment.Center 
            });
            importStack.Children.Add(new TextBlock 
            { 
                Text = "استيراد البيانات", 
                FontSize = 16, 
                Foreground = Brushes.White, 
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 5)
            });
            importStack.Children.Add(new TextBlock 
            { 
                Text = "استيراد من Excel", 
                FontSize = 12, 
                Foreground = Brushes.White, 
                HorizontalAlignment = HorizontalAlignment.Center 
            });
            
            importCard.Child = importStack;
            optionsGrid.Children.Add(importCard);

            // Export Data Card
            var exportCard = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(10, 10, 10, 10),
                Padding = new Thickness(20, 20, 20, 20),
                Cursor = Cursors.Hand
            };
            Grid.SetColumn(exportCard, 1);
            
            var exportStack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            exportStack.Children.Add(new TextBlock 
            { 
                Text = "📤", 
                FontSize = 48, 
                HorizontalAlignment = HorizontalAlignment.Center 
            });
            exportStack.Children.Add(new TextBlock 
            { 
                Text = "تصدير البيانات", 
                FontSize = 16, 
                Foreground = Brushes.White, 
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 5)
            });
            exportStack.Children.Add(new TextBlock 
            { 
                Text = "تصدير إلى Excel", 
                FontSize = 12, 
                Foreground = Brushes.White, 
                HorizontalAlignment = HorizontalAlignment.Center 
            });
            
            exportCard.Child = exportStack;
            optionsGrid.Children.Add(exportCard);
            
            dataContent.Children.Add(optionsGrid);
            MainContent.Content = dataContent;
        }

        private void ReportsBtn_Click(object sender, RoutedEventArgs e)
        {
            ContentTitle.Text = "التقارير";
            
            var reportsContent = new StackPanel();
            
            var header = new TextBlock
            {
                Text = "التقارير والإحصائيات",
                FontSize = 22,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            reportsContent.Children.Add(header);
            
            // Reports grid
            var reportsGrid = new Grid();
            reportsGrid.ColumnDefinitions.Add(new ColumnDefinition());
            reportsGrid.ColumnDefinitions.Add(new ColumnDefinition());
            reportsGrid.ColumnDefinitions.Add(new ColumnDefinition());
            reportsGrid.Margin = new Thickness(0, 20, 0, 0);
            
            // Monthly Report
            var monthlyCard = CreateReportCard("📈", "التقرير الشهري", "تقرير شامل للأنشطة الشهرية", "#FF9800");
            Grid.SetColumn(monthlyCard, 0);
            reportsGrid.Children.Add(monthlyCard);

            // Project Report
            var projectCard = CreateReportCard("📊", "تقرير المشاريع", "تقرير حالة المشاريع", "#9C27B0");
            Grid.SetColumn(projectCard, 1);
            reportsGrid.Children.Add(projectCard);

            // Financial Report
            var financialCard = CreateReportCard("💰", "التقرير المالي", "تقرير الميزانية والمصروفات", "#F44336");
            Grid.SetColumn(financialCard, 2);
            reportsGrid.Children.Add(financialCard);
            
            reportsContent.Children.Add(reportsGrid);
            MainContent.Content = reportsContent;
        }

        private void UsersBtn_Click(object sender, RoutedEventArgs e)
        {
            ContentTitle.Text = "إدارة المستخدمين";
            
            var usersContent = new StackPanel();
            
            var header = new TextBlock
            {
                Text = "إدارة المستخدمين",
                FontSize = 22,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            usersContent.Children.Add(header);
            
            // Users management content
            var usersInfo = new TextBlock
            {
                Text = "هنا يمكنك إدارة المستخدمين وصلاحياتهم في النظام",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 0)
            };
            usersContent.Children.Add(usersInfo);
            
            MainContent.Content = usersContent;
        }
        
        private Border CreateReportCard(string icon, string title, string description, string color)
        {
            var card = new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(10, 10, 10, 10),
                Padding = new Thickness(20, 20, 20, 20),
                Cursor = Cursors.Hand
            };
            
            var stack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            stack.Children.Add(new TextBlock 
            { 
                Text = icon, 
                FontSize = 48, 
                HorizontalAlignment = HorizontalAlignment.Center 
            });
            stack.Children.Add(new TextBlock 
            { 
                Text = title, 
                FontSize = 16, 
                Foreground = Brushes.White, 
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 5),
                FontWeight = FontWeights.Bold
            });
            stack.Children.Add(new TextBlock 
            { 
                Text = description, 
                FontSize = 12, 
                Foreground = Brushes.White, 
                HorizontalAlignment = HorizontalAlignment.Center,
                TextWrapping = TextWrapping.Wrap
            });
            
            card.Child = stack;
            return card;
        }

        private void AddProjectBtn_Click(object sender, RoutedEventArgs e)
        {
            var addProjectWindow = new AddProjectWindow();
            addProjectWindow.Owner = this;

            if (addProjectWindow.ShowDialog() == true && addProjectWindow.IsProjectSaved)
            {
                // Add the new project to the list
                projects.Add(addProjectWindow.NewProject);

                // Refresh the projects view
                RefreshProjectsView();
            }
        }

        private void RefreshProjectsView()
        {
            // Re-click the project management button to refresh the view
            ProjectManagementBtn_Click(null, null);
        }

        private void IpttDesignButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var project = button?.DataContext as Project;

            if (project != null)
            {
                var ipttWindow = new IpttDesignWindow(project);
                ipttWindow.Owner = this;
                ipttWindow.ShowDialog();
            }
        }
    }
}
