using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.IO;
using Microsoft.Win32;
using DrawingColor = System.Drawing.Color;
using MediaColor = System.Windows.Media.Color;
using MediaBrushes = System.Windows.Media.Brushes;

namespace ProjectManagementApp
{
    /// <summary>
    /// Interaction logic for IpttDesignWindow.xaml
    /// </summary>
    public partial class IpttDesignWindow : Window
    {
        private Project currentProject;
        private ObservableCollection<IpttIndicator> ipttData;
        private List<string> monthColumns;

        public IpttDesignWindow(Project project)
        {
            InitializeComponent();
            currentProject = project;
            InitializeWindow();
            GenerateIpttTable();
        }

        private void InitializeWindow()
        {
            ProjectNameText.Text = currentProject.ProjectName;
            ProjectTitle.Text = currentProject.ProjectName;
            ProjectManager.Text = currentProject.ProjectManager;
            
            var duration = (currentProject.EndDate - currentProject.StartDate).Days;
            var months = Math.Ceiling(duration / 30.0);
            ProjectDuration.Text = $"{months:F0} شهر من {currentProject.StartDateFormatted} إلى {currentProject.EndDateFormatted}";
        }

        private void GenerateIpttTable()
        {
            // Generate month columns based on project duration
            monthColumns = GenerateMonthColumns();
            
            // Initialize IPTT data with sample indicators
            InitializeIpttData();
            
            // Create DataGrid columns
            CreateDataGridColumns();
            
            // Bind data to DataGrid
            IpttDataGrid.ItemsSource = ipttData;
        }

        private List<string> GenerateMonthColumns()
        {
            var columns = new List<string>();
            var current = new DateTime(currentProject.StartDate.Year, currentProject.StartDate.Month, 1);
            var end = new DateTime(currentProject.EndDate.Year, currentProject.EndDate.Month, 1);

            while (current <= end)
            {
                var monthName = GetArabicMonthName(current.Month);
                var yearShort = current.Year.ToString().Substring(2);
                columns.Add($"{monthName}-{yearShort}");
                current = current.AddMonths(1);
            }

            return columns;
        }

        private string GetArabicMonthName(int month)
        {
            string[] arabicMonths = {
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
            return arabicMonths[month - 1];
        }

        private void InitializeIpttData()
        {
            ipttData = new ObservableCollection<IpttIndicator>();

            // Add sample health indicators similar to the Excel image
            var indicator1 = new IpttIndicator
            {
                No = "1.1",
                Indicator = "% من الأشخاص المستضعفين الذين تحسنت إمكانية وصولهم للخدمات الصحية من خلال حزمة MEP",
                TotalTarget = "60%",
                AchievementPercentage = "0%"
            };
            indicator1.DataTypes.Add(new DataTypeItem { Name = "% من الأشخاص المستضعفين الذين تحسنت إمكانية وصولهم للخدمات الصحية", Target = "60%" });
            ipttData.Add(indicator1);

            var indicator2 = new IpttIndicator
            {
                No = "1.1.1",
                Indicator = "# الزيارات الطبية الخارجية (OR) التي تم إجراؤها",
                TotalTarget = "27",
                AchievementPercentage = "100%"
            };
            indicator2.DataTypes.Add(new DataTypeItem { Name = "إجمالي", Target = "27" });
            ipttData.Add(indicator2);

            var indicator3 = new IpttIndicator
            {
                No = "1.1.2",
                Indicator = "# من الأطفال الذين تم فحصهم للكشف عن سوء التغذية الحاد",
                TotalTarget = "200",
                AchievementPercentage = "100%"
            };
            indicator3.DataTypes.Add(new DataTypeItem { Name = "إجمالي", Target = "200" });
            ipttData.Add(indicator3);

            var indicator4 = new IpttIndicator
            {
                No = "1.1.3",
                Indicator = "# من زيارات الرعاية السابقة للولادة المدعومة",
                TotalTarget = "500",
                AchievementPercentage = "130%"
            };
            indicator4.DataTypes.Add(new DataTypeItem { Name = "إجمالي", Target = "500" });
            ipttData.Add(indicator4);

            var indicator5 = new IpttIndicator
            {
                No = "1.1.4",
                Indicator = "# من الولادات المساعدة",
                TotalTarget = "300",
                AchievementPercentage = "85%"
            };
            indicator5.DataTypes.Add(new DataTypeItem { Name = "إجمالي", Target = "300" });
            ipttData.Add(indicator5);

            var indicator6 = new IpttIndicator
            {
                No = "1.1.5",
                Indicator = "# من الأطفال دون سن 1 الذين تلقوا التطعيم",
                TotalTarget = "887",
                AchievementPercentage = "64%"
            };
            // Add multiple data types for this indicator
            indicator6.DataTypes.Add(new DataTypeItem { Name = "# من الأولاد", Target = "425" });
            indicator6.DataTypes.Add(new DataTypeItem { Name = "# من البنات", Target = "462" });
            indicator6.DataTypes.Add(new DataTypeItem { Name = "إجمالي # من الأطفال", Target = "887" });
            ipttData.Add(indicator6);

            // Add another example with multiple data types
            var indicator7 = new IpttIndicator
            {
                No = "1.1.13",
                Indicator = "# من المستفيدين الذين تلقوا جلسات توعية",
                TotalTarget = "1000",
                AchievementPercentage = "75%"
            };
            indicator7.DataTypes.Add(new DataTypeItem { Name = "# من الرجال", Target = "400" });
            indicator7.DataTypes.Add(new DataTypeItem { Name = "# من النساء", Target = "350" });
            indicator7.DataTypes.Add(new DataTypeItem { Name = "# من الأولاد", Target = "125" });
            indicator7.DataTypes.Add(new DataTypeItem { Name = "# من البنات", Target = "125" });
            indicator7.DataTypes.Add(new DataTypeItem { Name = "إجمالي", Target = "1000" });
            ipttData.Add(indicator7);

            // Initialize monthly data for each indicator and data type
            foreach (var indicator in ipttData)
            {
                indicator.MonthlyData = new Dictionary<string, string>();
                foreach (var month in monthColumns)
                {
                    indicator.MonthlyData[month] = "0";
                }

                // Initialize monthly data for each data type
                foreach (var dataType in indicator.DataTypes)
                {
                    dataType.MonthlyValues = new Dictionary<string, string>();
                    foreach (var month in monthColumns)
                    {
                        dataType.MonthlyValues[month] = "0";
                    }
                }
            }
        }

        private void CreateDataGridColumns()
        {
            IpttDataGrid.Columns.Clear();

            // Fixed columns
            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "رقم",
                Binding = new Binding("No"),
                Width = 50,
                IsReadOnly = true
            });

            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "المؤشر",
                Binding = new Binding("Indicator"),
                Width = 300,
                IsReadOnly = false
            });

            // Create custom column for data types
            var dataTypeColumn = new DataGridTemplateColumn
            {
                Header = "نوع البيانات",
                Width = 200
            };

            // Create template for displaying data types
            var dataTypeTemplate = new DataTemplate();
            var stackPanelFactory = new FrameworkElementFactory(typeof(StackPanel));

            var textBlockFactory = new FrameworkElementFactory(typeof(TextBlock));
            textBlockFactory.SetBinding(TextBlock.TextProperty, new Binding("DataTypesDisplay"));
            textBlockFactory.SetValue(TextBlock.TextWrappingProperty, TextWrapping.Wrap);
            textBlockFactory.SetValue(TextBlock.FontSizeProperty, 10.0);
            textBlockFactory.SetValue(TextBlock.VerticalAlignmentProperty, VerticalAlignment.Center);

            var buttonFactory = new FrameworkElementFactory(typeof(Button));
            buttonFactory.SetValue(Button.ContentProperty, "تحرير");
            buttonFactory.SetValue(Button.FontSizeProperty, 9.0);
            buttonFactory.SetValue(Button.PaddingProperty, new Thickness(5, 2, 5, 2));
            buttonFactory.SetValue(Button.MarginProperty, new Thickness(0, 2, 0, 0));
            buttonFactory.SetValue(Button.BackgroundProperty, new SolidColorBrush(MediaColor.FromRgb(156, 39, 176)));
            buttonFactory.SetValue(Button.ForegroundProperty, MediaBrushes.White);
            buttonFactory.SetValue(Button.BorderThicknessProperty, new Thickness(0, 0, 0, 0));
            buttonFactory.SetValue(Button.CursorProperty, Cursors.Hand);
            buttonFactory.AddHandler(Button.ClickEvent, new RoutedEventHandler(EditDataTypesButton_Click));

            stackPanelFactory.AppendChild(textBlockFactory);
            stackPanelFactory.AppendChild(buttonFactory);
            dataTypeTemplate.VisualTree = stackPanelFactory;
            dataTypeColumn.CellTemplate = dataTypeTemplate;

            IpttDataGrid.Columns.Add(dataTypeColumn);

            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "الهدف الإجمالي",
                Binding = new Binding("TotalTarget"),
                Width = 100,
                IsReadOnly = false
            });

            IpttDataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "نسبة الإنجاز",
                Binding = new Binding("AchievementPercentage"),
                Width = 100,
                IsReadOnly = false
            });

            // Monthly columns
            foreach (var month in monthColumns)
            {
                var column = new DataGridTextColumn
                {
                    Header = month,
                    Width = 80,
                    IsReadOnly = false
                };

                // Create binding for monthly data
                var binding = new Binding($"MonthlyData[{month}]")
                {
                    Mode = BindingMode.TwoWay,
                    UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged
                };
                column.Binding = binding;

                IpttDataGrid.Columns.Add(column);
            }
        }

        private void SaveIpttButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Here you would save the IPTT data to database or file
                MessageBox.Show("تم حفظ بيانات IPTT بنجاح!", "نجح الحفظ", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set EPPlus license context
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"IPTT_{currentProject.ProjectName}_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportToExcel(saveFileDialog.FileName);

                    var result = MessageBox.Show("تم تصدير IPTT إلى Excel بنجاح!\n\nهل تريد فتح الملف؟",
                                               "نجح التصدير",
                                               MessageBoxButton.YesNo,
                                               MessageBoxImage.Information);

                    if (result == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveFileDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التصدير:\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddIndicatorButton_Click(object sender, RoutedEventArgs e)
        {
            var newIndicator = new IpttIndicator
            {
                No = (ipttData.Count + 1).ToString(),
                Indicator = "مؤشر جديد",
                TotalTarget = "0",
                AchievementPercentage = "0%",
                MonthlyData = new Dictionary<string, string>()
            };

            // Add default data type
            newIndicator.DataTypes.Add(new DataTypeItem
            {
                Name = "إجمالي",
                Target = "0",
                MonthlyValues = new Dictionary<string, string>()
            });

            // Initialize monthly data
            foreach (var month in monthColumns)
            {
                newIndicator.MonthlyData[month] = "0";
                newIndicator.DataTypes[0].MonthlyValues[month] = "0";
            }

            ipttData.Add(newIndicator);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void EditDataTypesButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var indicator = button?.DataContext as IpttIndicator;

            if (indicator != null)
            {
                var dataTypesWindow = new DataTypesEditorWindow(indicator, monthColumns);
                dataTypesWindow.Owner = this;
                if (dataTypesWindow.ShowDialog() == true)
                {
                    // Refresh the display
                    IpttDataGrid.Items.Refresh();
                }
            }
        }

        private void ExportToExcel(string filePath)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("IPTT");

                // Set RTL direction for Arabic text
                worksheet.View.RightToLeft = true;

                // Project information header
                CreateProjectInfoSection(worksheet);

                // IPTT table
                CreateIpttTable(worksheet);

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Save the file
                var fileInfo = new FileInfo(filePath);
                package.SaveAs(fileInfo);
            }
        }

        private void CreateProjectInfoSection(ExcelWorksheet worksheet)
        {
            // Title
            worksheet.Cells["A1"].Value = "جدول المؤشرات والأهداف (IPTT)";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells["A1:M1"].Merge = true;

            // Project information
            worksheet.Cells["A3"].Value = "اسم المشروع:";
            worksheet.Cells["B3"].Value = currentProject.ProjectName;
            worksheet.Cells["A3"].Style.Font.Bold = true;

            worksheet.Cells["A4"].Value = "مدير المشروع:";
            worksheet.Cells["B4"].Value = currentProject.ProjectManager;
            worksheet.Cells["A4"].Style.Font.Bold = true;

            var duration = (currentProject.EndDate - currentProject.StartDate).Days;
            var months = Math.Ceiling(duration / 30.0);
            worksheet.Cells["A5"].Value = "مدة المشروع:";
            worksheet.Cells["B5"].Value = $"{months:F0} شهر من {currentProject.StartDateFormatted} إلى {currentProject.EndDateFormatted}";
            worksheet.Cells["A5"].Style.Font.Bold = true;

            worksheet.Cells["A6"].Value = "مواقع المشروع:";
            worksheet.Cells["B6"].Value = "محافظة إب (مديرية السياني)";
            worksheet.Cells["A6"].Style.Font.Bold = true;
        }

        private void CreateIpttTable(ExcelWorksheet worksheet)
        {
            int startRow = 9;
            int currentRow = startRow;

            // Create headers
            var headers = new List<string> { "رقم", "المؤشر", "نوع البيانات", "الهدف الإجمالي", "نسبة الإنجاز" };
            headers.AddRange(monthColumns);

            for (int col = 0; col < headers.Count; col++)
            {
                var cell = worksheet.Cells[currentRow, col + 1];
                cell.Value = headers[col];
                cell.Style.Font.Bold = true;
                // Set background color for header
                cell.Style.Fill.BackgroundColor.SetColor(DrawingColor.FromArgb(255, 152, 0)); // Orange
                cell.Style.Font.Color.SetColor(DrawingColor.White);
                cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            currentRow++;

            // Add data rows
            foreach (var indicator in ipttData)
            {
                if (indicator.DataTypes.Count == 0)
                {
                    // Single row for indicator without data types
                    AddIndicatorRow(worksheet, currentRow, indicator, "", "", headers.Count);
                    currentRow++;
                }
                else if (indicator.DataTypes.Count == 1)
                {
                    // Single row for indicator with one data type
                    var dataType = indicator.DataTypes[0];
                    AddIndicatorRow(worksheet, currentRow, indicator, dataType.Name, dataType.Target, headers.Count);
                    currentRow++;
                }
                else
                {
                    // Multiple rows for indicator with multiple data types
                    for (int i = 0; i < indicator.DataTypes.Count; i++)
                    {
                        var dataType = indicator.DataTypes[i];
                        if (i == 0)
                        {
                            // First row shows indicator info
                            AddIndicatorRow(worksheet, currentRow, indicator, dataType.Name, dataType.Target, headers.Count);
                        }
                        else
                        {
                            // Subsequent rows show only data type info
                            AddDataTypeRow(worksheet, currentRow, dataType, headers.Count);
                        }
                        currentRow++;
                    }
                }
            }

            // Apply borders to the entire table
            var tableRange = worksheet.Cells[startRow, 1, currentRow - 1, headers.Count];
            tableRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            tableRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;
        }

        private void AddIndicatorRow(ExcelWorksheet worksheet, int row, IpttIndicator indicator, string dataTypeName, string dataTypeTarget, int totalColumns)
        {
            worksheet.Cells[row, 1].Value = indicator.No;
            worksheet.Cells[row, 2].Value = indicator.Indicator;
            worksheet.Cells[row, 3].Value = dataTypeName;
            worksheet.Cells[row, 4].Value = string.IsNullOrEmpty(dataTypeTarget) ? indicator.TotalTarget : dataTypeTarget;
            worksheet.Cells[row, 5].Value = indicator.AchievementPercentage;

            // Add monthly data
            for (int i = 0; i < monthColumns.Count; i++)
            {
                var month = monthColumns[i];
                var value = indicator.MonthlyData.ContainsKey(month) ? indicator.MonthlyData[month] : "0";
                worksheet.Cells[row, 6 + i].Value = value;
            }

            // Center align numeric columns
            for (int col = 1; col <= totalColumns; col++)
            {
                worksheet.Cells[row, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[row, col].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            }
        }

        private void AddDataTypeRow(ExcelWorksheet worksheet, int row, DataTypeItem dataType, int totalColumns)
        {
            worksheet.Cells[row, 1].Value = ""; // No number for sub-rows
            worksheet.Cells[row, 2].Value = ""; // No indicator text for sub-rows
            worksheet.Cells[row, 3].Value = dataType.Name;
            worksheet.Cells[row, 4].Value = dataType.Target;
            worksheet.Cells[row, 5].Value = ""; // No achievement percentage for sub-rows

            // Add monthly data for this data type
            for (int i = 0; i < monthColumns.Count; i++)
            {
                var month = monthColumns[i];
                var value = dataType.MonthlyValues.ContainsKey(month) ? dataType.MonthlyValues[month] : "0";
                worksheet.Cells[row, 6 + i].Value = value;
            }

            // Center align and add light background for sub-rows
            for (int col = 1; col <= totalColumns; col++)
            {
                worksheet.Cells[row, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[row, col].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                // Set light background for sub-rows
                worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(DrawingColor.FromArgb(248, 249, 250));
            }
        }
    }

    // IPTT Indicator data model
    public class IpttIndicator
    {
        public string No { get; set; } = "";
        public string Indicator { get; set; } = "";
        public ObservableCollection<DataTypeItem> DataTypes { get; set; } = new ObservableCollection<DataTypeItem>();
        public string DataType { get; set; } = ""; // For display purposes
        public string TotalTarget { get; set; } = "";
        public string AchievementPercentage { get; set; } = "";
        public Dictionary<string, string> MonthlyData { get; set; } = new Dictionary<string, string>();

        // Property to display data types as formatted string
        public string DataTypesDisplay
        {
            get
            {
                if (DataTypes.Count == 0) return "";
                return string.Join("\n", DataTypes.Select(dt => dt.Name));
            }
        }


    }

    // Data Type Item model
    public class DataTypeItem
    {
        public string Name { get; set; } = "";
        public string Target { get; set; } = "";
        public Dictionary<string, string> MonthlyValues { get; set; } = new Dictionary<string, string>();
    }
}
