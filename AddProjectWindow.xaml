<Window x:Class="ProjectManagementApp.AddProjectWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="إضافة مشروع جديد" 
        Height="700" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="70"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2E7D32" CornerRadius="0,0,15,15">
            <Grid>
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderPlus" 
                                           Width="32" 
                                           Height="32" 
                                           Foreground="White"
                                           Margin="0,0,15,0"/>
                    <TextBlock Text="إضافة مشروع جديد" 
                              FontSize="24" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" 
                     VerticalScrollBarVisibility="Auto" 
                     Margin="30,20,30,20">
            <StackPanel>
                <!-- Project Basic Info Card -->
                <Border Background="White" 
                        CornerRadius="12" 
                        Margin="0,0,0,20"
                        Padding="25">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.2"/>
                    </Border.Effect>
                    
                    <StackPanel>
                        <TextBlock Text="المعلومات الأساسية للمشروع" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#2E7D32"
                                  Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Project ID -->
                            <StackPanel Grid.Row="0" Grid.Column="0">
                                <TextBlock Text="رقم المشروع *" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <TextBox Name="ProjectIdTextBox"
                                        Height="40"
                                        Padding="12,8"
                                        FontSize="14"
                                        BorderBrush="#ddd"
                                        BorderThickness="2"
                                        Background="White"
                                        KeyDown="ProjectIdTextBox_KeyDown"/>
                            </StackPanel>
                            
                            <!-- Project Status -->
                            <StackPanel Grid.Row="0" Grid.Column="2">
                                <TextBlock Text="حالة المشروع *" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <ComboBox Name="ProjectStatusComboBox" 
                                         Height="40"
                                         Padding="12,8"
                                         FontSize="14"
                                         BorderBrush="#ddd"
                                         BorderThickness="2"
                                         Background="White">
                                    <ComboBoxItem Content="نشط"/>
                                    <ComboBoxItem Content="قيد التنفيذ"/>
                                    <ComboBoxItem Content="مكتمل"/>
                                    <ComboBoxItem Content="متوقف"/>
                                    <ComboBoxItem Content="ملغي"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <!-- Project Name -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3">
                                <TextBlock Text="اسم المشروع *" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <TextBox Name="ProjectNameTextBox"
                                        Height="40"
                                        Padding="12,8"
                                        FontSize="14"
                                        BorderBrush="#ddd"
                                        BorderThickness="2"
                                        Background="White"
                                        KeyDown="ProjectNameTextBox_KeyDown"/>
                            </StackPanel>
                            
                            <!-- Project Manager -->
                            <StackPanel Grid.Row="4" Grid.Column="0">
                                <TextBlock Text="مدير المشروع *" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <TextBox Name="ProjectManagerTextBox"
                                        Height="40"
                                        Padding="12,8"
                                        FontSize="14"
                                        BorderBrush="#ddd"
                                        BorderThickness="2"
                                        Background="White"
                                        KeyDown="ProjectManagerTextBox_KeyDown"/>
                            </StackPanel>
                            
                            <!-- Project Budget -->
                            <StackPanel Grid.Row="4" Grid.Column="2">
                                <TextBlock Text="ميزانية المشروع" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <TextBox Name="ProjectBudgetTextBox"
                                        Height="40"
                                        Padding="12,8"
                                        FontSize="14"
                                        BorderBrush="#ddd"
                                        BorderThickness="2"
                                        Background="White"
                                        Text="0"
                                        KeyDown="ProjectBudgetTextBox_KeyDown"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Project Dates Card -->
                <Border Background="White" 
                        CornerRadius="12" 
                        Margin="0,0,0,20"
                        Padding="25">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.2"/>
                    </Border.Effect>
                    
                    <StackPanel>
                        <TextBlock Text="التواريخ والمدة الزمنية" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#2E7D32"
                                  Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Start Date -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="تاريخ البداية *" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <DatePicker Name="StartDatePicker" 
                                           Height="40"
                                           Padding="12,8"
                                           FontSize="14"
                                           BorderBrush="#ddd"
                                           BorderThickness="2"
                                           Background="White"/>
                            </StackPanel>
                            
                            <!-- End Date -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="تاريخ النهاية *" 
                                          FontWeight="SemiBold" 
                                          Margin="0,0,0,5"
                                          Foreground="#333"/>
                                <DatePicker Name="EndDatePicker" 
                                           Height="40"
                                           Padding="12,8"
                                           FontSize="14"
                                           BorderBrush="#ddd"
                                           BorderThickness="2"
                                           Background="White"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Project Description Card -->
                <Border Background="White" 
                        CornerRadius="12" 
                        Margin="0,0,0,20"
                        Padding="25">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.2"/>
                    </Border.Effect>
                    
                    <StackPanel>
                        <TextBlock Text="وصف المشروع والملاحظات" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#2E7D32"
                                  Margin="0,0,0,20"/>
                        
                        <StackPanel>
                            <TextBlock Text="وصف المشروع" 
                                      FontWeight="SemiBold" 
                                      Margin="0,0,0,5"
                                      Foreground="#333"/>
                            <TextBox Name="ProjectDescriptionTextBox" 
                                    Height="100"
                                    Padding="12,8"
                                    FontSize="14"
                                    BorderBrush="#ddd"
                                    BorderThickness="2"
                                    Background="White"
                                    TextWrapping="Wrap"
                                    AcceptsReturn="True"
                                    VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" 
                Background="White" 
                BorderBrush="#e0e0e0" 
                BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                
                <Button Name="SaveButton" 
                        Content="حفظ المشروع" 
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="SaveButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="ContentSave" 
                                                           Width="20" 
                                                           Height="20" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#45a049"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#3d8b40"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
                
                <Button Name="CancelButton" 
                        Content="إلغاء" 
                        Width="140"
                        Height="45"
                        Margin="10"
                        Background="#f44336"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="CancelButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    CornerRadius="8"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <StackPanel Orientation="Horizontal" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Cancel" 
                                                           Width="20" 
                                                           Height="20" 
                                                           Margin="0,0,8,0"/>
                                    <ContentPresenter/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#da190b"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#c62828"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
